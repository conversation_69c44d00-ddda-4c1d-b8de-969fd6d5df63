import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer, Route, Routes } from 'react-router-dom';
import { Container, Navbar, Nav } from 'react-bootstrap';
import { ToastContainer } from 'react-toastify';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-toastify/dist/ReactToastify.css';

// Import ML components
import MLTrainingInterface from './components/MLTrainingInterface';
import MLDashboard from './components/MLDashboard';
import DocumentTester from './components/DocumentTester';
import MLDocumentTraining from './components/MLDocumentTraining';

// Home component
const Home = () => {
  return (
    <Container className="mt-4">
      <div className="text-center mb-5">
        <h1 className="display-4">🤖 ML Document Classification System</h1>
        <p className="lead">Intelligent document identification and classification using machine learning</p>
      </div>

      <div className="row">
        <div className="col-md-4 mb-4">
          <div className="card h-100">
            <div className="card-body text-center">
              <h5 className="card-title">🎯 ML Training</h5>
              <p className="card-text">Train custom document classification models with your own data.</p>
              <a href="/ml-training" className="btn btn-primary">Start Training</a>
            </div>
          </div>
        </div>

        <div className="col-md-4 mb-4">
          <div className="card h-100">
            <div className="card-body text-center">
              <h5 className="card-title">📄 Document Tester</h5>
              <p className="card-text">Test document classification with trained models.</p>
              <a href="/document-tester" className="btn btn-success">Test Documents</a>
            </div>
          </div>
        </div>

        <div className="col-md-4 mb-4">
          <div className="card h-100">
            <div className="card-body text-center">
              <h5 className="card-title">📊 ML Dashboard</h5>
              <p className="card-text">Monitor system performance and training metrics.</p>
              <a href="/ml-dashboard" className="btn btn-info">View Dashboard</a>
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-5">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5>📋 Supported Document Types</h5>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <ul className="list-unstyled">
                    <li>✅ Aadhar Card - Indian national identity card</li>
                    <li>✅ Voter ID - Election commission voter identity card</li>
                    <li>✅ Driving License - Transport department driving license</li>
                    <li>✅ Passport - International travel document</li>
                  </ul>
                </div>
                <div className="col-md-6">
                  <ul className="list-unstyled">
                    <li>✅ PAN Card - Permanent account number tax card</li>
                    <li>✅ Bank Statement - Financial institution statement</li>
                    <li>✅ Utility Bill - Service provider billing document</li>
                    <li>✅ Unknown - Unidentified or other document types</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-4">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5>🚀 Quick Start</h5>
            </div>
            <div className="card-body">
              <ol>
                <li>Start with the <strong>ML Training</strong> interface to upload and train your model</li>
                <li>Use the <strong>Document Tester</strong> to validate your trained model</li>
                <li>Monitor performance with the <strong>ML Dashboard</strong></li>
              </ol>
              <div className="alert alert-info mt-3">
                <strong>Note:</strong> Make sure the server is running on port 5000 for API connectivity.
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

// Navigation component
const Navigation = () => {
  return (
    <Navbar bg="dark" variant="dark" expand="lg">
      <Container>
        <Navbar.Brand href="/">🤖 ML Document Classification</Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link href="/">Home</Nav.Link>
            <Nav.Link href="/ml-training">ML Training</Nav.Link>
            <Nav.Link href="/document-tester">Document Tester</Nav.Link>
            <Nav.Link href="/ml-dashboard">Dashboard</Nav.Link>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

// Main App component
function App() {
  return (
    <BrowserRouter>
      <div className="App">
        <Navigation />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/ml-training" element={<MLTrainingInterface />} />
          <Route path="/ml-document-training" element={<MLDocumentTraining />} />
          <Route path="/document-tester" element={<DocumentTester />} />
          <Route path="/ml-dashboard" element={<MLDashboard />} />
        </Routes>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
    </BrowserRouter>
  );
}

export default App;
