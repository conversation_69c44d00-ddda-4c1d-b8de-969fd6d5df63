# 🎯 Complete Training Interfaces & Methods Guide

This guide provides all available methods to train, upload, and manage ML models with step-by-step instructions.

## 🌐 Web Interface (Recommended)

### Access the Training Interface
1. **Start the server:**
   ```bash
   cd server
   node simple-server.js
   ```

2. **Open in browser:**
   ```
   http://localhost:5000/ml-training
   ```

### Interface Features

#### 📤 Upload Data Tab
- **Document Type Selection**: Choose from 8 document types
- **Dataset Type**: Training (60%), Validation (20%), Test (20%)
- **Drag & Drop Upload**: Support for JPG, PNG, GIF, BMP
- **Real-time Statistics**: View current dataset distribution
- **Batch Upload**: Multiple files at once

#### 🎯 Train Model Tab
- **Parameter Configuration**: Epochs, batch size, learning rate
- **Training Session Management**: Initialize and start training
- **Real-time Progress**: Live progress bar and metrics
- **Training Logs**: Detailed epoch-by-epoch progress
- **Stop/Resume**: Control training process

#### 🧪 Test Model Tab
- **Single Image Testing**: Upload and classify documents
- **Detailed Results**: Confidence scores and probabilities
- **Performance Metrics**: Track model accuracy
- **Authenticity Check**: Fraud detection results

#### 📊 Manage Models Tab
- **Model Operations**: Download, upload, backup, restore
- **Model Information**: Version, size, accuracy, last trained
- **Export/Import**: Share models between systems
- **Reset Options**: Restore to default state

#### 📈 View Results Tab
- **Training History**: All past training sessions
- **Performance Analytics**: Charts and graphs
- **Recent Classifications**: Latest test results
- **Export Data**: Download results for analysis

## 🖥️ Command Line Interface

### Interactive Training Script
```bash
cd server
node scripts/trainModel.js
```

**Available Options:**
1. **Initialize system** - Set up ML environment
2. **Create sample training data** - Generate directory structure
3. **Upload training data from directory** - Import images
4. **View training data statistics** - Check dataset balance
5. **Configure training parameters** - Adjust hyperparameters
6. **Start model training** - Begin training process
7. **Test trained model** - Classify test images
8. **View training history** - Review past sessions
9. **Clean up old data** - Remove outdated files

### Quick Demo Training
```bash
cd server
npm run ml-simple-demo
```
- Complete training simulation with mock data
- Shows full workflow in 2-3 minutes
- Perfect for testing and demonstration

### Advanced Training Script
```bash
cd server
node scripts/trainWithExample.js
```
- Creates synthetic training data
- Demonstrates complete training pipeline
- Includes data augmentation and validation

## 🔧 API Endpoints

### Training Management
```javascript
// Initialize training session
POST /api/ml-documents/training/session/init
Body: { config: { epochs: 50, batchSize: 32, learningRate: 0.001 } }

// Upload training samples
POST /api/ml-documents/training/samples/upload
FormData: { samples: [files], documentType: "aadhar", datasetType: "training" }

// Start training
POST /api/ml-documents/training/start
Body: { sessionId: "training_123456789" }

// Get training progress
GET /api/ml-documents/training/session/stats

// Get training statistics
GET /api/ml-documents/training/stats
```

### Model Operations
```javascript
// Classify document
POST /api/ml-documents/classify
FormData: { document: file }

// Download model
GET /api/ml-documents/model/download

// Upload model
POST /api/ml-documents/model/upload
FormData: { modelFiles: [files] }

// Backup model
POST /api/ml-documents/model/backup

// Restore model
POST /api/ml-documents/model/restore
```

### Results & Analytics
```javascript
// Get training history
GET /api/ml-documents/training/history

// Get recent classifications
GET /api/ml-documents/classifications/recent

// Export training history
GET /api/ml-documents/training/history/export

// Export classifications
GET /api/ml-documents/classifications/export
```

## 📋 Step-by-Step Training Process

### Method 1: Web Interface (Easiest)
1. **Start Server**: `node server/simple-server.js`
2. **Open Browser**: `http://localhost:5000/ml-training`
3. **Upload Data**: Use "Upload Data" tab
4. **Configure**: Set parameters in "Train Model" tab
5. **Initialize**: Click "Initialize Training Session"
6. **Train**: Click "Start Training"
7. **Monitor**: Watch real-time progress
8. **Test**: Use "Test Model" tab to verify results

### Method 2: Command Line (Advanced)
1. **Initialize**: `node scripts/trainModel.js` → Option 1
2. **Create Structure**: Option 2
3. **Add Images**: Place files in `sample_data/` folders
4. **Upload**: Option 3 → Enter path to `sample_data`
5. **Configure**: Option 5 (optional)
6. **Train**: Option 6
7. **Test**: Option 7

### Method 3: Quick Demo (Testing)
1. **Run Demo**: `node scripts/simpleTrainingDemo.js`
2. **Watch Output**: Complete simulation in 2-3 minutes
3. **Review Results**: 90%+ accuracy with mock data

## 🎯 Training Data Requirements

### Minimum Setup
- **10 images per document type**
- **5 document types minimum**
- **JPG/PNG format**
- **Clear, well-lit images**

### Recommended Setup
- **50-100 images per document type**
- **All 8 document types**
- **Various conditions** (lighting, angles, quality)
- **Balanced distribution** across types

### Production Setup
- **200+ images per document type**
- **Multiple document variants**
- **Edge cases and difficult samples**
- **Regular data updates**

## 📊 Expected Results

### Training Metrics
- **Good Model**: 85%+ accuracy
- **Excellent Model**: 95%+ accuracy
- **Production Ready**: 98%+ accuracy

### Training Time
- **Small Dataset** (50 images): 5-10 minutes
- **Medium Dataset** (200 images): 15-30 minutes
- **Large Dataset** (500+ images): 30-60 minutes

## 🔄 Model Management

### Backup & Restore
```bash
# Web Interface
- Go to "Manage Models" tab
- Click "Backup Current Model"
- Click "Restore from Backup" when needed

# API
POST /api/ml-documents/model/backup
POST /api/ml-documents/model/restore
```

### Export & Import
```bash
# Export trained model
GET /api/ml-documents/model/export

# Import pre-trained model
POST /api/ml-documents/model/upload
```

### Version Control
- Models are automatically versioned
- Training history is preserved
- Rollback to previous versions available

## 🚨 Troubleshooting

### Common Issues & Solutions

**Issue**: "No training files found"
**Solution**: Ensure images are in JPG/PNG format in correct folders

**Issue**: "Training session failed"
**Solution**: Check MongoDB connection and restart server

**Issue**: "Low accuracy results"
**Solution**: Add more training data and balance dataset

**Issue**: "Out of memory"
**Solution**: Reduce batch size to 16 or 8

**Issue**: "Web interface not loading"
**Solution**: Ensure server is running on port 5000

## 🎉 Success Verification

After training, verify your setup:

1. **✅ Web Interface**: Accessible at `http://localhost:5000/ml-training`
2. **✅ Upload Works**: Can upload training images
3. **✅ Training Completes**: Achieves >85% accuracy
4. **✅ Testing Works**: Can classify test images
5. **✅ Model Management**: Can backup/restore models
6. **✅ API Responds**: All endpoints return valid data

## 📞 Getting Help

If you encounter issues:

1. **Check Server**: Ensure `node server/simple-server.js` is running
2. **Verify Port**: Confirm port 5000 is available
3. **Test Demo**: Run `node scripts/simpleTrainingDemo.js`
4. **Check Logs**: Review console output for errors
5. **Browser Console**: Check for JavaScript errors

---

**🎯 You now have complete access to all training interfaces and methods. Choose the one that best fits your needs and start training your document classification model!**
