# 🚀 ML Document Classification Training Demo

This guide walks you through training the ML model with example data step by step.

## 🎯 Quick Demo (One Command)

```bash
# Navigate to server directory
cd server

# Install dependencies (if not already done)
npm install

# Run complete demo with synthetic data
npm run ml-demo
```

This will:
1. ✅ Setup the ML system
2. ✅ Generate 100 synthetic document images (20 per type)
3. ✅ Organize data into train/validation/test sets
4. ✅ Train the model for 10 epochs
5. ✅ Test the trained model
6. ✅ Show results and accuracy

## 📋 Step-by-Step Demo

### Step 1: Setup System
```bash
cd server
npm install
npm run setup-ml
```

### Step 2: Run Training Example
```bash
npm run train-example
```

### Step 3: View Results
The script will show:
- Training progress
- Final accuracy
- Test results
- Sample predictions

## 🎮 Interactive Training

For more control, use the interactive training script:

```bash
npm run train-ml
```

Then follow the menu:
1. **Initialize system** (option 1)
2. **Create sample training data** (option 2)
3. **Upload training data** (option 3)
4. **Configure training** (option 5) - optional
5. **Start training** (option 6)
6. **Test model** (option 7)

## 📊 What the Demo Creates

### Synthetic Training Data
The demo generates realistic document images for:

- **Aadhar Cards** (20 samples)
  - Government of India header
  - Name, DOB, Aadhar number
  - Address and photo placeholder
  
- **Voter ID Cards** (20 samples)
  - Election Commission header
  - Voter details and ID number
  - Constituency information
  
- **Driving Licenses** (20 samples)
  - Transport Department header
  - Driver details and DL number
  - Validity information
  
- **Passports** (20 samples)
  - Republic of India header
  - Passport holder details
  - Passport number and place of birth
  
- **PAN Cards** (20 samples)
  - Income Tax Department header
  - PAN holder details
  - PAN number and signature area

### Data Split
- **Training**: 60% (12 samples per type)
- **Validation**: 20% (4 samples per type)  
- **Test**: 20% (4 samples per type)

### Training Configuration
```javascript
{
  epochs: 10,           // Quick training for demo
  batchSize: 8,         // Small batch size
  learningRate: 0.001,  // Standard learning rate
  validationSplit: 0.2  // 20% for validation
}
```

## 📈 Expected Results

### Training Progress
```
Epoch 1/10: loss = 2.0456, accuracy = 0.2500
Epoch 2/10: loss = 1.8234, accuracy = 0.3750
Epoch 3/10: loss = 1.5678, accuracy = 0.5000
...
Epoch 10/10: loss = 0.3456, accuracy = 0.8750
```

### Final Results
- **Accuracy**: 70-90% (with synthetic data)
- **Training Time**: 2-5 minutes
- **Model Size**: ~50MB

### Test Output Example
```
🧪 Testing with: aadhar_sample_001.jpg
📋 Expected type: aadhar
🎯 Predicted type: aadhar
📊 Confidence: 85.67%
✅ Correct: YES

📊 Top predictions:
   1. aadhar: 85.67%
   2. voter_id: 8.23%
   3. pan_card: 3.45%
```

## 🌐 Web Interface Demo

After training, start the web server:

```bash
npm run dev
```

Then visit:
- **Training Interface**: http://localhost:5000/ml-training
- **Document Tester**: http://localhost:5000/document-tester
- **ML Dashboard**: http://localhost:5000/ml-dashboard

### Training Interface Features
- 📤 Upload real document images
- ⚙️ Configure training parameters
- 📊 Monitor training progress
- 📈 View training metrics

### Document Tester Features
- 🖼️ Upload test documents
- 🎯 View classification results
- 🔍 Detailed feature analysis
- 🛡️ Authenticity validation

## 🔧 Customizing the Demo

### Change Training Parameters
Edit `server/scripts/trainWithExample.js`:

```javascript
const EXAMPLE_CONFIG = {
  epochs: 20,           // More epochs for better accuracy
  batchSize: 16,        // Larger batch size
  learningRate: 0.0005, // Lower learning rate
  validationSplit: 0.2,
  samplesPerClass: 50   // More samples per type
};
```

### Add More Document Types
Modify the `createSyntheticDocument` function to add new document types:

```javascript
case 'bank_statement':
  ctx.fillText('BANK STATEMENT', 120, 50);
  // Add bank statement specific content
  break;
```

### Use Real Images
1. Create directories in `server/sample_data/`
2. Add real document images
3. Run: `npm run train-ml` (option 3)

## 🚨 Troubleshooting

### Common Issues

#### 1. Canvas/Node-Canvas Issues
```bash
# Ubuntu/Debian
sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev

# macOS
brew install pkg-config cairo pango libpng jpeg giflib librsvg

# Windows
# Use pre-built binaries or WSL
```

#### 2. TensorFlow Issues
```bash
# Rebuild TensorFlow
npm rebuild @tensorflow/tfjs-node

# Or install from source
npm install @tensorflow/tfjs-node --build-from-source
```

#### 3. Memory Issues
- Reduce batch size to 4 or 8
- Reduce number of samples
- Close other applications

#### 4. Low Accuracy
- Increase epochs (20-50)
- Add more training data
- Improve data quality
- Balance dataset

### Performance Tips

#### For Better Accuracy
1. **More Data**: 100+ samples per document type
2. **Real Images**: Use actual document photos
3. **Data Quality**: Clear, well-lit images
4. **Balanced Dataset**: Equal samples per type
5. **More Epochs**: 50-100 epochs for production

#### For Faster Training
1. **Smaller Batch Size**: 8-16 for limited memory
2. **Fewer Epochs**: 10-20 for quick testing
3. **GPU Support**: Use `@tensorflow/tfjs-node-gpu`
4. **SSD Storage**: Store training data on SSD

## 📚 Next Steps

After running the demo:

1. **Add Real Data**: Replace synthetic images with real documents
2. **Increase Dataset**: Add 100+ samples per document type
3. **Fine-tune Parameters**: Adjust epochs, batch size, learning rate
4. **Production Training**: Train for 50-100 epochs
5. **Deploy Model**: Use the trained model in production
6. **Monitor Performance**: Track accuracy and retrain as needed

## 🎉 Success Metrics

### Demo Success
- ✅ System initializes without errors
- ✅ Synthetic data generates successfully
- ✅ Training completes without crashes
- ✅ Model achieves >70% accuracy
- ✅ Test predictions work correctly

### Production Ready
- ✅ >95% accuracy on real test data
- ✅ Fast inference (<1 second per document)
- ✅ Robust to various image qualities
- ✅ Low false positive rate
- ✅ Handles edge cases gracefully

---

## 🚀 Ready to Start?

Run the complete demo:

```bash
cd server
npm install
npm run ml-demo
```

Or step-by-step:

```bash
cd server
npm run setup-ml
npm run train-example
npm run dev
```

**Happy Training! 🤖📚**
