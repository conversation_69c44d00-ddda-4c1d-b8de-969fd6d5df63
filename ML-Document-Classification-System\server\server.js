const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const mongoose = require('mongoose');

// Load environment variables
dotenv.config();

// Import middleware
const errorHandler = require('./utils/errorHandler');
const logger = require('./utils/logger');

// Import routes
const mlDocumentRoutes = require('./routes/mlDocumentRoutes');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ml-document-classification';
    await mongoose.connect(mongoURI);
    logger.info('MongoDB connected successfully');
  } catch (error) {
    logger.error('MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Connect to database
connectDB();

// Routes
app.use('/api/ml-documents', mlDocumentRoutes);

// Serve ML training interface
app.get('/ml-training', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>ML Training Interface</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🎯 ML Document Classification Training Interface</h1>
        
        <div class="card">
          <h2>🚀 Quick Start</h2>
          <p>To use the full React interface, start the client application:</p>
          <pre>cd ../client && npm start</pre>
          <p>Then visit: <a href="http://localhost:3000/ml-training">http://localhost:3000/ml-training</a></p>
        </div>

        <div class="card">
          <h2>🎮 Training Commands</h2>
          <ul>
            <li><code>npm run train-ml</code> - Interactive training menu</li>
            <li><code>npm run ml-simple-demo</code> - Simple demo (no dependencies)</li>
            <li><code>npm run ml-demo</code> - Full demo with synthetic data</li>
            <li><code>npm run setup-ml</code> - Setup ML system</li>
          </ul>
        </div>

        <div class="card">
          <h2>📊 API Endpoints</h2>
          <ul>
            <li><a href="/api/ml-documents/document-types">GET /api/ml-documents/document-types</a> - Supported document types</li>
            <li>POST /api/ml-documents/classify - Classify a document</li>
            <li>POST /api/ml-documents/training/start - Start training</li>
            <li>GET /api/ml-documents/training/session/stats - Training statistics</li>
          </ul>
        </div>

        <div class="card">
          <h2>🤖 System Status</h2>
          <p>Server is running on port ${PORT}</p>
          <p>Environment: ${process.env.NODE_ENV || 'development'}</p>
          <p>MongoDB: ${mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'}</p>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Document tester interface
app.get('/document-tester', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Document Tester</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .btn { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #1e7e34; }
        input[type="file"] { margin: 10px 0; }
        #result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>📄 Document Classification Tester</h1>
        
        <div class="card">
          <h2>Upload Document</h2>
          <input type="file" id="fileInput" accept="image/*">
          <button class="btn" onclick="classifyDocument()">Classify Document</button>
          <div id="result"></div>
        </div>

        <div class="card">
          <h2>📋 Supported Document Types</h2>
          <ul>
            <li>Aadhar Card</li>
            <li>Voter ID</li>
            <li>Driving License</li>
            <li>Passport</li>
            <li>PAN Card</li>
            <li>Bank Statement</li>
            <li>Utility Bill</li>
          </ul>
        </div>
      </div>

      <script>
        async function classifyDocument() {
          const fileInput = document.getElementById('fileInput');
          const resultDiv = document.getElementById('result');
          
          if (!fileInput.files[0]) {
            resultDiv.innerHTML = '<p style="color: red;">Please select a file first.</p>';
            return;
          }

          const formData = new FormData();
          formData.append('document', fileInput.files[0]);

          try {
            resultDiv.innerHTML = '<p>Classifying document...</p>';
            
            const response = await fetch('/api/ml-documents/classify', {
              method: 'POST',
              body: formData
            });

            const data = await response.json();
            
            if (data.success) {
              resultDiv.innerHTML = \`
                <h3>Classification Results</h3>
                <p><strong>Document Type:</strong> \${data.classification.documentType}</p>
                <p><strong>Confidence:</strong> \${(data.classification.confidence * 100).toFixed(2)}%</p>
                <p><strong>Processing Time:</strong> \${data.processingTime}ms</p>
              \`;
            } else {
              resultDiv.innerHTML = \`<p style="color: red;">Error: \${data.error}</p>\`;
            }
          } catch (error) {
            resultDiv.innerHTML = \`<p style="color: red;">Error: \${error.message}</p>\`;
          }
        }
      </script>
    </body>
    </html>
  `);
});

// Default route
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>ML Document Classification System</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🤖 ML Document Classification System</h1>
          <p>Intelligent document identification and classification using machine learning</p>
        </div>

        <div class="card">
          <h2>🎯 Web Interfaces</h2>
          <a href="/ml-training" class="btn">ML Training Interface</a>
          <a href="/document-tester" class="btn btn-success">Document Tester</a>
          <a href="http://localhost:3000" class="btn btn-warning">Full React App</a>
        </div>

        <div class="card">
          <h2>📊 API Endpoints</h2>
          <ul>
            <li><a href="/api/ml-documents/document-types">GET /api/ml-documents/document-types</a> - Supported document types</li>
            <li>POST /api/ml-documents/classify - Classify a document</li>
            <li>POST /api/ml-documents/training/start - Start training</li>
            <li>GET /api/ml-documents/training/session/stats - Training statistics</li>
          </ul>
        </div>

        <div class="card">
          <h2>🚀 Quick Commands</h2>
          <pre>
# Interactive training
npm run train-ml

# Simple demo (no dependencies)
npm run ml-simple-demo

# Full demo with synthetic data
npm run ml-demo

# Setup ML system
npm run setup-ml
          </pre>
        </div>

        <div class="card">
          <h2>📋 Supported Document Types</h2>
          <ul>
            <li>Aadhar Card - Indian national identity card</li>
            <li>Voter ID - Election commission voter identity card</li>
            <li>Driving License - Transport department driving license</li>
            <li>Passport - International travel document</li>
            <li>PAN Card - Permanent account number tax card</li>
            <li>Bank Statement - Financial institution statement</li>
            <li>Utility Bill - Service provider billing document</li>
            <li>Unknown - Unidentified or other document types</li>
          </ul>
        </div>

        <div class="card">
          <h2>🔧 System Status</h2>
          <p><strong>Server:</strong> Running on port ${PORT}</p>
          <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'development'}</p>
          <p><strong>MongoDB:</strong> ${mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'}</p>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>
        </div>
      </div>
    </body>
    </html>
  `);
});

// Error handling middleware
app.use(errorHandler.notFound);
app.use(errorHandler.errorHandler);

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`ML Document Classification Server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`Server time: ${new Date().toISOString()}`);
  logger.info(`Access the system at: http://localhost:${PORT}`);
});

module.exports = app;
