/**
 * Training Data Manager Module
 * 
 * This module manages training data collection, validation, preprocessing,
 * and organization for document classification models.
 */

const fs = require('fs');
const path = require('path');
const { DocumentClassification, TrainingSession } = require('../models/DocumentClassification');
const documentClassification = require('./documentClassification');

// Training data paths
const TRAINING_DATA_ROOT = path.join(__dirname, '../training_data');
const ORGANIZED_DATA_PATH = path.join(TRAINING_DATA_ROOT, 'organized');
const RAW_DATA_PATH = path.join(TRAINING_DATA_ROOT, 'raw');
const AUGMENTED_DATA_PATH = path.join(TRAINING_DATA_ROOT, 'augmented');

// Document type directories
const DOCUMENT_TYPES = Object.values(documentClassification.DOCUMENT_TYPES);

// Ensure directory structure exists
function initializeDirectoryStructure() {
  const basePaths = [TRAINING_DATA_ROOT, ORGANIZED_DATA_PATH, RAW_DATA_PATH, AUGMENTED_DATA_PATH];
  
  basePaths.forEach(basePath => {
    if (!fs.existsSync(basePath)) {
      fs.mkdirSync(basePath, { recursive: true });
    }
    
    // Create subdirectories for each document type
    DOCUMENT_TYPES.forEach(docType => {
      const docTypePath = path.join(basePath, docType);
      if (!fs.existsSync(docTypePath)) {
        fs.mkdirSync(docTypePath, { recursive: true });
      }
      
      // Create train/val/test subdirectories
      ['train', 'validation', 'test'].forEach(split => {
        const splitPath = path.join(docTypePath, split);
        if (!fs.existsSync(splitPath)) {
          fs.mkdirSync(splitPath, { recursive: true });
        }
      });
    });
  });
  
  console.log('Training data directory structure initialized');
}

/**
 * Validate training data file
 * @param {string} filePath - Path to the file
 * @param {string} documentType - Expected document type
 * @returns {Promise<Object>} - Validation result
 */
async function validateTrainingFile(filePath, documentType) {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return {
        valid: false,
        error: 'File does not exist'
      };
    }
    
    // Check file size
    const stats = fs.statSync(filePath);
    if (stats.size === 0) {
      return {
        valid: false,
        error: 'File is empty'
      };
    }
    
    if (stats.size > 10 * 1024 * 1024) { // 10MB limit
      return {
        valid: false,
        error: 'File size exceeds 10MB limit'
      };
    }
    
    // Check document type validity
    if (!DOCUMENT_TYPES.includes(documentType)) {
      return {
        valid: false,
        error: `Invalid document type: ${documentType}`
      };
    }
    
    // Validate image format
    const imageBuffer = fs.readFileSync(filePath);
    try {
      await documentClassification.preprocessImage(imageBuffer);
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid image format or corrupted file'
      };
    }
    
    // Extract features for quality check
    const featuresResult = await documentClassification.extractDocumentFeatures(imageBuffer);
    if (!featuresResult.success) {
      return {
        valid: false,
        error: 'Failed to extract features from image'
      };
    }
    
    // Quality checks
    const qualityIssues = [];
    
    if (featuresResult.layoutFeatures) {
      if (featuresResult.layoutFeatures.brightness < 30) {
        qualityIssues.push('Image too dark');
      }
      if (featuresResult.layoutFeatures.brightness > 220) {
        qualityIssues.push('Image overexposed');
      }
      if (featuresResult.layoutFeatures.contrast < 15) {
        qualityIssues.push('Low contrast');
      }
      if (featuresResult.layoutFeatures.textDensity < 0.005) {
        qualityIssues.push('Very low text density');
      }
    }
    
    return {
      valid: true,
      qualityIssues: qualityIssues,
      features: featuresResult,
      fileStats: {
        size: stats.size,
        lastModified: stats.mtime
      }
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message
    };
  }
}

/**
 * Organize training data into proper directory structure
 * @param {Array} trainingFiles - Array of training file objects
 * @returns {Promise<Object>} - Organization result
 */
async function organizeTrainingData(trainingFiles) {
  try {
    const results = {
      organized: 0,
      failed: 0,
      errors: []
    };
    
    for (const file of trainingFiles) {
      try {
        // Validate the file
        const validation = await validateTrainingFile(file.filePath, file.documentType);
        
        if (!validation.valid) {
          results.failed++;
          results.errors.push({
            file: file.filePath,
            error: validation.error
          });
          continue;
        }
        
        // Determine split (default to training if not specified)
        const split = file.split || 'train';
        
        // Create organized file path
        const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${path.basename(file.filePath)}`;
        const organizedPath = path.join(ORGANIZED_DATA_PATH, file.documentType, split, fileName);
        
        // Copy file to organized location
        fs.copyFileSync(file.filePath, organizedPath);
        
        // Save to database
        const docClassification = new DocumentClassification({
          originalFilename: path.basename(file.filePath),
          filePath: organizedPath,
          fileSize: validation.fileStats.size,
          mimeType: 'image/jpeg', // Assume JPEG for now
          classificationResult: {
            documentType: file.documentType,
            confidence: 1.0, // Manual labeling assumed to be correct
            probabilities: {
              [file.documentType]: 1.0
            }
          },
          extractedFeatures: validation.features,
          validationResult: {
            isAuthentic: true, // Assume training data is authentic
            confidence: 1.0,
            authenticityScore: 1.0,
            riskFactors: validation.qualityIssues
          },
          processingTime: 0,
          manualVerification: {
            isVerified: true,
            actualDocumentType: file.documentType,
            isAuthentic: true,
            verificationNotes: 'Training data - manually labeled'
          },
          isTrainingData: true,
          trainingDataset: split === 'train' ? 'training' : split
        });
        
        await docClassification.save();
        
        results.organized++;
        console.log(`Organized training file: ${fileName} -> ${file.documentType}/${split}`);
        
      } catch (error) {
        results.failed++;
        results.errors.push({
          file: file.filePath,
          error: error.message
        });
      }
    }
    
    return {
      success: true,
      results: results
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get training data statistics
 * @returns {Promise<Object>} - Training data statistics
 */
async function getTrainingDataStats() {
  try {
    const stats = {
      totalSamples: 0,
      byDocumentType: {},
      byDataset: {
        training: 0,
        validation: 0,
        test: 0
      },
      qualityIssues: {
        total: 0,
        byType: {}
      }
    };
    
    // Get data from database
    const trainingData = await DocumentClassification.find({ isTrainingData: true });
    
    stats.totalSamples = trainingData.length;
    
    trainingData.forEach(doc => {
      // Count by document type
      const docType = doc.classificationResult.documentType;
      stats.byDocumentType[docType] = (stats.byDocumentType[docType] || 0) + 1;
      
      // Count by dataset
      const dataset = doc.trainingDataset;
      if (stats.byDataset[dataset] !== undefined) {
        stats.byDataset[dataset]++;
      }
      
      // Count quality issues
      if (doc.validationResult.riskFactors && doc.validationResult.riskFactors.length > 0) {
        stats.qualityIssues.total++;
        doc.validationResult.riskFactors.forEach(issue => {
          stats.qualityIssues.byType[issue] = (stats.qualityIssues.byType[issue] || 0) + 1;
        });
      }
    });
    
    // Get file system stats
    const fileSystemStats = {};
    DOCUMENT_TYPES.forEach(docType => {
      fileSystemStats[docType] = {
        train: 0,
        validation: 0,
        test: 0
      };
      
      ['train', 'validation', 'test'].forEach(split => {
        const dirPath = path.join(ORGANIZED_DATA_PATH, docType, split);
        if (fs.existsSync(dirPath)) {
          const files = fs.readdirSync(dirPath).filter(file => 
            file.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp)$/));
          fileSystemStats[docType][split] = files.length;
        }
      });
    });
    
    return {
      success: true,
      databaseStats: stats,
      fileSystemStats: fileSystemStats
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Prepare training dataset for model training
 * @param {Object} options - Dataset preparation options
 * @returns {Promise<Object>} - Prepared dataset information
 */
async function prepareTrainingDataset(options = {}) {
  try {
    const {
      includeAugmentation = false,
      balanceClasses = true,
      minSamplesPerClass = 10,
      maxSamplesPerClass = 1000
    } = options;
    
    // Get training data from database
    const trainingData = await DocumentClassification.find({ 
      isTrainingData: true,
      trainingDataset: 'training'
    });
    
    const validationData = await DocumentClassification.find({ 
      isTrainingData: true,
      trainingDataset: 'validation'
    });
    
    const testData = await DocumentClassification.find({ 
      isTrainingData: true,
      trainingDataset: 'test'
    });
    
    // Organize by document type
    const organizedData = {
      training: {},
      validation: {},
      test: {}
    };
    
    // Group training data
    trainingData.forEach(doc => {
      const docType = doc.classificationResult.documentType;
      if (!organizedData.training[docType]) {
        organizedData.training[docType] = [];
      }
      organizedData.training[docType].push(doc);
    });
    
    // Group validation data
    validationData.forEach(doc => {
      const docType = doc.classificationResult.documentType;
      if (!organizedData.validation[docType]) {
        organizedData.validation[docType] = [];
      }
      organizedData.validation[docType].push(doc);
    });
    
    // Group test data
    testData.forEach(doc => {
      const docType = doc.classificationResult.documentType;
      if (!organizedData.test[docType]) {
        organizedData.test[docType] = [];
      }
      organizedData.test[docType].push(doc);
    });
    
    // Check class balance and minimum requirements
    const classStats = {};
    DOCUMENT_TYPES.forEach(docType => {
      const trainCount = organizedData.training[docType]?.length || 0;
      const valCount = organizedData.validation[docType]?.length || 0;
      const testCount = organizedData.test[docType]?.length || 0;
      
      classStats[docType] = {
        training: trainCount,
        validation: valCount,
        test: testCount,
        total: trainCount + valCount + testCount
      };
    });
    
    // Identify classes with insufficient data
    const insufficientClasses = DOCUMENT_TYPES.filter(docType => 
      classStats[docType].training < minSamplesPerClass);
    
    if (insufficientClasses.length > 0) {
      console.warn(`Classes with insufficient training data: ${insufficientClasses.join(', ')}`);
    }
    
    return {
      success: true,
      dataset: organizedData,
      classStats: classStats,
      insufficientClasses: insufficientClasses,
      recommendations: {
        needMoreData: insufficientClasses,
        balanceIssues: Object.keys(classStats).filter(docType => 
          classStats[docType].training > maxSamplesPerClass || 
          classStats[docType].training < minSamplesPerClass
        )
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Clean up old training files
 * @param {number} daysOld - Remove files older than this many days
 * @returns {Promise<Object>} - Cleanup result
 */
async function cleanupOldTrainingFiles(daysOld = 30) {
  try {
    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));
    let removedCount = 0;
    
    // Find old training data in database
    const oldData = await DocumentClassification.find({
      isTrainingData: true,
      createdAt: { $lt: cutoffDate }
    });
    
    for (const doc of oldData) {
      try {
        // Remove file if it exists
        if (fs.existsSync(doc.filePath)) {
          fs.unlinkSync(doc.filePath);
        }
        
        // Remove from database
        await DocumentClassification.deleteOne({ _id: doc._id });
        removedCount++;
      } catch (error) {
        console.error(`Failed to remove old training file: ${doc.filePath}`, error);
      }
    }
    
    return {
      success: true,
      removedCount: removedCount
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// Initialize directory structure on module load
initializeDirectoryStructure();

module.exports = {
  initializeDirectoryStructure,
  validateTrainingFile,
  organizeTrainingData,
  getTrainingDataStats,
  prepareTrainingDataset,
  cleanupOldTrainingFiles,
  TRAINING_DATA_ROOT,
  ORGANIZED_DATA_PATH,
  RAW_DATA_PATH,
  AUGMENTED_DATA_PATH
};
