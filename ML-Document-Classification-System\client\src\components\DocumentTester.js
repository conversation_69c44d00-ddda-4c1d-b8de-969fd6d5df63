/**
 * Document Tester Component
 * 
 * This component provides an interface for testing the trained document
 * classification model with new document images.
 */

import React, { useState, useRef } from 'react';
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Form,
  Alert,
  Badge,
  ProgressBar,
  Table,
  Modal
} from 'react-bootstrap';
import { Doughnut, Bar } from 'react-chartjs-2';

const DocumentTester = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const fileInputRef = useRef(null);

  const addAlert = (message, variant = 'info') => {
    const alert = {
      id: Date.now(),
      message,
      variant
    };
    setAlerts(prev => [...prev, alert]);
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleFileSelection = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        addAlert('Please select an image file', 'warning');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        addAlert('File size must be less than 10MB', 'warning');
        return;
      }

      setSelectedFile(file);
      
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      
      // Clear previous results
      setResults(null);
    }
  };

  const classifyDocument = async () => {
    if (!selectedFile) {
      addAlert('Please select a document image first', 'warning');
      return;
    }

    setIsProcessing(true);
    
    try {
      const formData = new FormData();
      formData.append('document', selectedFile);

      const response = await fetch('/api/ml-documents/classify', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.success) {
        setResults(data);
        addAlert('Document classified successfully!', 'success');
      } else {
        addAlert(`Classification failed: ${data.error}`, 'danger');
      }
    } catch (error) {
      addAlert(`Error: ${error.message}`, 'danger');
    } finally {
      setIsProcessing(false);
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setResults(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Chart data for classification probabilities
  const getProbabilityChartData = () => {
    if (!results?.classification?.probabilities) return null;

    const probabilities = results.classification.probabilities;
    const labels = Object.keys(probabilities).map(key => 
      key.replace('_', ' ').toUpperCase()
    );
    const data = Object.values(probabilities);

    return {
      labels,
      datasets: [{
        data,
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#FF6384',
          '#C9CBCF'
        ],
        borderWidth: 2
      }]
    };
  };

  // Chart data for feature analysis
  const getFeatureAnalysisData = () => {
    if (!results?.features?.layoutFeatures) return null;

    const features = results.features.layoutFeatures;
    
    return {
      labels: ['Brightness', 'Contrast', 'Text Density', 'Edge Density'],
      datasets: [{
        label: 'Feature Values',
        data: [
          features.brightness / 255 * 100, // Normalize to percentage
          features.contrast / 100 * 100,   // Normalize to percentage
          features.textDensity * 1000,     // Scale up for visibility
          features.edgeDensity * 100       // Scale up for visibility
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    };
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'danger';
  };

  const getAuthenticityColor = (score) => {
    if (score >= 0.8) return 'success';
    if (score >= 0.5) return 'warning';
    return 'danger';
  };

  return (
    <Container fluid className="document-tester">
      {/* Alerts */}
      {alerts.map(alert => (
        <Alert key={alert.id} variant={alert.variant} className="mb-3">
          {alert.message}
        </Alert>
      ))}

      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h4>Document Classification Tester</h4>
              <p className="mb-0 text-muted">
                Upload a document image to test the trained ML model
              </p>
            </Card.Header>
            <Card.Body>
              <Row>
                {/* Upload Section */}
                <Col md={6}>
                  <Card className="h-100">
                    <Card.Header>Upload Document</Card.Header>
                    <Card.Body>
                      <Form.Group className="mb-3">
                        <Form.Label>Select Document Image</Form.Label>
                        <Form.Control
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileSelection}
                        />
                        <Form.Text className="text-muted">
                          Supported formats: JPG, PNG, GIF, BMP (Max 10MB)
                        </Form.Text>
                      </Form.Group>

                      {previewUrl && (
                        <div className="mb-3">
                          <img
                            src={previewUrl}
                            alt="Document preview"
                            style={{
                              maxWidth: '100%',
                              maxHeight: '300px',
                              objectFit: 'contain',
                              border: '1px solid #ddd',
                              borderRadius: '4px'
                            }}
                          />
                          <div className="mt-2">
                            <small className="text-muted">
                              File: {selectedFile?.name} ({Math.round(selectedFile?.size / 1024)}KB)
                            </small>
                          </div>
                        </div>
                      )}

                      <div className="d-grid gap-2">
                        <Button
                          variant="primary"
                          onClick={classifyDocument}
                          disabled={!selectedFile || isProcessing}
                        >
                          {isProcessing ? 'Processing...' : 'Classify Document'}
                        </Button>
                        <Button
                          variant="secondary"
                          onClick={clearSelection}
                          disabled={!selectedFile}
                        >
                          Clear
                        </Button>
                      </div>

                      {isProcessing && (
                        <div className="mt-3">
                          <ProgressBar animated now={100} />
                          <small className="text-muted">
                            Analyzing document with ML model...
                          </small>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>

                {/* Results Section */}
                <Col md={6}>
                  <Card className="h-100">
                    <Card.Header>Classification Results</Card.Header>
                    <Card.Body>
                      {results ? (
                        <div>
                          {/* Main Classification Result */}
                          <div className="mb-4">
                            <h5>Document Type</h5>
                            <div className="d-flex align-items-center mb-2">
                              <Badge 
                                bg="primary" 
                                className="me-2"
                                style={{ fontSize: '1rem', padding: '0.5rem 1rem' }}
                              >
                                {results.classification.documentType.replace('_', ' ').toUpperCase()}
                              </Badge>
                              <Badge 
                                bg={getConfidenceColor(results.classification.confidence)}
                                style={{ fontSize: '0.9rem' }}
                              >
                                {(results.classification.confidence * 100).toFixed(1)}% confidence
                              </Badge>
                            </div>
                          </div>

                          {/* Authenticity Check */}
                          {results.validation && (
                            <div className="mb-4">
                              <h5>Authenticity Check</h5>
                              <div className="d-flex align-items-center mb-2">
                                <Badge 
                                  bg={results.validation.isAuthentic ? 'success' : 'danger'}
                                  className="me-2"
                                  style={{ fontSize: '1rem', padding: '0.5rem 1rem' }}
                                >
                                  {results.validation.isAuthentic ? 'AUTHENTIC' : 'SUSPICIOUS'}
                                </Badge>
                                <Badge 
                                  bg={getAuthenticityColor(results.validation.authenticityScore)}
                                  style={{ fontSize: '0.9rem' }}
                                >
                                  {(results.validation.authenticityScore * 100).toFixed(1)}% score
                                </Badge>
                              </div>
                              
                              {results.validation.riskFactors && results.validation.riskFactors.length > 0 && (
                                <div>
                                  <small className="text-muted">Risk Factors:</small>
                                  <ul className="list-unstyled">
                                    {results.validation.riskFactors.map((factor, index) => (
                                      <li key={index}>
                                        <Badge bg="warning" className="me-1">!</Badge>
                                        <small>{factor}</small>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Quick Stats */}
                          <div className="mb-3">
                            <h6>Processing Info</h6>
                            <Table size="sm">
                              <tbody>
                                <tr>
                                  <td>Processing Time</td>
                                  <td>{results.processingTime || 'N/A'}ms</td>
                                </tr>
                                <tr>
                                  <td>Model Version</td>
                                  <td>{results.modelVersion || '1.0.0'}</td>
                                </tr>
                                <tr>
                                  <td>Timestamp</td>
                                  <td>{new Date(results.timestamp).toLocaleString()}</td>
                                </tr>
                              </tbody>
                            </Table>
                          </div>

                          <Button
                            variant="info"
                            size="sm"
                            onClick={() => setShowDetailsModal(true)}
                          >
                            View Detailed Analysis
                          </Button>
                        </div>
                      ) : (
                        <div className="text-center text-muted">
                          <p>Upload and classify a document to see results here</p>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Detailed Results Modal */}
      <Modal show={showDetailsModal} onHide={() => setShowDetailsModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Detailed Classification Analysis</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {results && (
            <Row>
              {/* Probability Distribution */}
              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>Classification Probabilities</Card.Header>
                  <Card.Body>
                    {getProbabilityChartData() && (
                      <Doughnut
                        data={getProbabilityChartData()}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom'
                            }
                          }
                        }}
                        height={250}
                      />
                    )}
                  </Card.Body>
                </Card>
              </Col>

              {/* Feature Analysis */}
              <Col md={6}>
                <Card className="mb-3">
                  <Card.Header>Feature Analysis</Card.Header>
                  <Card.Body>
                    {getFeatureAnalysisData() && (
                      <Bar
                        data={getFeatureAnalysisData()}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          scales: {
                            y: {
                              beginAtZero: true
                            }
                          }
                        }}
                        height={250}
                      />
                    )}
                  </Card.Body>
                </Card>
              </Col>

              {/* Text Features */}
              {results.features?.textFeatures && (
                <Col md={12}>
                  <Card className="mb-3">
                    <Card.Header>Extracted Text Features</Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={6}>
                          <h6>Pattern Detection</h6>
                          <Table size="sm">
                            <tbody>
                              <tr>
                                <td>Aadhar Number</td>
                                <td>
                                  <Badge bg={results.features.textFeatures.hasAadharNumber ? 'success' : 'secondary'}>
                                    {results.features.textFeatures.hasAadharNumber ? 'Found' : 'Not Found'}
                                  </Badge>
                                </td>
                              </tr>
                              <tr>
                                <td>Voter ID Number</td>
                                <td>
                                  <Badge bg={results.features.textFeatures.hasVoterIdNumber ? 'success' : 'secondary'}>
                                    {results.features.textFeatures.hasVoterIdNumber ? 'Found' : 'Not Found'}
                                  </Badge>
                                </td>
                              </tr>
                              <tr>
                                <td>Driving License Number</td>
                                <td>
                                  <Badge bg={results.features.textFeatures.hasDrivingLicenseNumber ? 'success' : 'secondary'}>
                                    {results.features.textFeatures.hasDrivingLicenseNumber ? 'Found' : 'Not Found'}
                                  </Badge>
                                </td>
                              </tr>
                              <tr>
                                <td>PAN Number</td>
                                <td>
                                  <Badge bg={results.features.textFeatures.hasPanNumber ? 'success' : 'secondary'}>
                                    {results.features.textFeatures.hasPanNumber ? 'Found' : 'Not Found'}
                                  </Badge>
                                </td>
                              </tr>
                            </tbody>
                          </Table>
                        </Col>
                        <Col md={6}>
                          <h6>Text Statistics</h6>
                          <Table size="sm">
                            <tbody>
                              <tr>
                                <td>Word Count</td>
                                <td>{results.features.textFeatures.wordCount}</td>
                              </tr>
                              <tr>
                                <td>Digit Count</td>
                                <td>{results.features.textFeatures.digitCount}</td>
                              </tr>
                              <tr>
                                <td>Uppercase Count</td>
                                <td>{results.features.textFeatures.upperCaseCount}</td>
                              </tr>
                              <tr>
                                <td>Government Keywords</td>
                                <td>
                                  <Badge bg={results.features.textFeatures.hasGovernmentKeywords ? 'success' : 'secondary'}>
                                    {results.features.textFeatures.hasGovernmentKeywords ? 'Found' : 'Not Found'}
                                  </Badge>
                                </td>
                              </tr>
                            </tbody>
                          </Table>
                        </Col>
                      </Row>
                      
                      {results.features.textFeatures.extractedText && (
                        <div className="mt-3">
                          <h6>Extracted Text</h6>
                          <div 
                            style={{
                              maxHeight: '200px',
                              overflowY: 'auto',
                              backgroundColor: '#f8f9fa',
                              padding: '10px',
                              borderRadius: '4px',
                              fontSize: '0.9rem'
                            }}
                          >
                            {results.features.textFeatures.extractedText}
                          </div>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              )}

              {/* Layout Features */}
              {results.features?.layoutFeatures && (
                <Col md={12}>
                  <Card>
                    <Card.Header>Layout Features</Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={3}>
                          <strong>Dimensions:</strong><br />
                          {results.features.layoutFeatures.width} × {results.features.layoutFeatures.height}
                        </Col>
                        <Col md={3}>
                          <strong>Aspect Ratio:</strong><br />
                          {results.features.layoutFeatures.aspectRatio?.toFixed(2)}
                        </Col>
                        <Col md={3}>
                          <strong>Brightness:</strong><br />
                          {results.features.layoutFeatures.brightness?.toFixed(1)}
                        </Col>
                        <Col md={3}>
                          <strong>Contrast:</strong><br />
                          {results.features.layoutFeatures.contrast?.toFixed(1)}
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
              )}
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDetailsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default DocumentTester;
