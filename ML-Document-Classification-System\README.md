# 🤖 ML Document Classification System

## Overview

This is a standalone Machine Learning Document Classification System that was separated from the Online Voting System. It provides intelligent document identification and classification capabilities using TensorFlow.js and advanced image processing techniques.

## 🎯 Features

- **Document Classification**: Automatically identify and classify various document types
- **Training Interface**: Web-based interface for training custom models
- **Real-time Testing**: Upload and test documents instantly
- **Performance Monitoring**: Track model accuracy and performance metrics
- **API Integration**: RESTful APIs for easy integration with other systems

## 📋 Supported Document Types

1. **Aadhar Card** - Indian national identity card
2. **Voter ID** - Election commission voter identity card  
3. **Driving License** - Transport department driving license
4. **Passport** - International travel document
5. **PAN Card** - Permanent account number tax card
6. **Bank Statement** - Financial institution statement
7. **Utility Bill** - Service provider billing document
8. **Unknown** - Unidentified or other document types

## 🏗️ System Architecture

```
ML-Document-Classification-System/
├── server/                 # Backend ML system
│   ├── ai/                # ML modules and algorithms
│   ├── models/            # Database models
│   ├── routes/            # API endpoints
│   ├── scripts/           # Training and setup scripts
│   ├── utils/             # Utility functions
│   ├── middleware/        # Express middleware
│   ├── config/            # Configuration files
│   ├── uploads/           # File upload storage
│   ├── training_data/     # Training datasets
│   └── sample_data/       # Sample training data
├── client/                # Frontend React application
│   └── src/
│       └── components/    # React components
├── docs/                  # Documentation
└── README.md             # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 14 or higher
- MongoDB (for data storage)
- At least 4GB RAM (8GB recommended for training)

### Installation

1. **Clone or extract the ML system**:
   ```bash
   cd ML-Document-Classification-System
   ```

2. **Install server dependencies**:
   ```bash
   cd server
   npm install
   ```

3. **Install client dependencies**:
   ```bash
   cd ../client
   npm install
   ```

4. **Setup the ML system**:
   ```bash
   cd ../server
   npm run setup-ml
   ```

### Running the System

1. **Start the server**:
   ```bash
   cd server
   npm run dev
   ```

2. **Start the client** (in a new terminal):
   ```bash
   cd client
   npm start
   ```

3. **Access the interfaces**:
   - ML Training Interface: `http://localhost:3000/ml-training`
   - Document Tester: `http://localhost:3000/document-tester`
   - ML Dashboard: `http://localhost:3000/ml-dashboard`

## 🎮 Training Your Model

### Option 1: Interactive Training (Recommended)
```bash
cd server
npm run train-ml
```

### Option 2: Web Interface
1. Start both server and client
2. Navigate to `http://localhost:3000/ml-training`
3. Upload training data with labels
4. Configure training parameters
5. Start training and monitor progress

### Option 3: Quick Demo
```bash
cd server
npm run ml-simple-demo  # Dependency-free demo
npm run ml-demo         # Full demo with synthetic data
```

## 📊 API Endpoints

### Document Classification
- `POST /api/ml-documents/classify` - Classify a document
- `GET /api/ml-documents/document-types` - Get supported types

### Training Management
- `POST /api/ml-documents/training/session/init` - Initialize training
- `POST /api/ml-documents/training/start` - Start training
- `GET /api/ml-documents/training/session/stats` - Get training stats

### Model Evaluation
- `POST /api/ml-documents/evaluate` - Evaluate model performance

## 🔧 Configuration

### Training Parameters
- **Epochs**: 50-100 for production models
- **Batch Size**: 32 (adjust based on available memory)
- **Learning Rate**: 0.001 (default)
- **Validation Split**: 20% of training data

### Data Requirements
- **Formats**: JPG, PNG, GIF, BMP
- **Size**: Maximum 10MB per image
- **Resolution**: Minimum 224x224 pixels
- **Quantity**: At least 100 samples per document type (200+ recommended)

## 📈 Performance Expectations

### With Real Training Data:
- **Training Time**: 10-30 minutes (depending on data size)
- **Expected Accuracy**: 85-95% (with 100+ samples per type)
- **Model Size**: ~50-100MB
- **Inference Speed**: <1 second per document

## 🔒 Security Features

- Input validation and sanitization
- File type and size restrictions
- Secure file upload handling
- Error logging and monitoring
- Rate limiting on API endpoints

## 🛠️ Development

### Available Scripts

```bash
# Server scripts
npm run dev              # Start development server
npm run start           # Start production server
npm run setup-ml        # Setup ML system
npm run train-ml        # Interactive training
npm run ml-demo         # Full training demo

# Client scripts
npm start               # Start React development server
npm run build          # Build for production
npm test               # Run tests
```

## 📚 Documentation

- **Training Guide**: `docs/ML_TRAINING_GUIDE.md`
- **API Documentation**: `docs/API_REFERENCE.md`
- **Deployment Guide**: `docs/DEPLOYMENT.md`
- **Troubleshooting**: `docs/TROUBLESHOOTING.md`

## 🤝 Integration

This system can be easily integrated with other applications:

1. **API Integration**: Use the REST API endpoints
2. **NPM Package**: Install as a dependency
3. **Docker**: Deploy using containerization
4. **Microservice**: Run as a standalone service

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
1. Check the documentation in the `docs/` folder
2. Review the troubleshooting guide
3. Check the GitHub issues (if applicable)

## 🎉 Success Metrics

✅ **System Implementation Complete**:
- All ML modules implemented and tested
- Database schemas created and integrated
- API endpoints functional and documented
- Frontend components ready for use
- Training scripts working correctly
- Demo successfully executed

**Ready for Production!** 🚀
