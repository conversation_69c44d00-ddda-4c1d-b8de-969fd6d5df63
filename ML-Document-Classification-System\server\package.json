{"name": "ml-document-classification-server", "version": "1.0.0", "description": "Machine Learning Document Classification System - Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-ml": "node scripts/setupMLSystem.js", "train-ml": "node scripts/trainModel.js", "train-example": "node scripts/trainWithExample.js", "train-demo": "node scripts/simpleTrainingDemo.js", "ml-full-setup": "npm run setup-ml && npm run train-ml", "ml-demo": "npm run setup-ml && npm run train-example", "ml-simple-demo": "node scripts/simpleTrainingDemo.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-node": "^4.10.0", "bcryptjs": "^2.4.3", "canvas": "^2.11.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "multer": "^1.4.5-lts.1", "node-tesseract-ocr": "^2.2.1", "sharp": "^0.32.6", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "keywords": ["machine-learning", "document-classification", "tensorflow", "image-processing", "ocr", "api"], "author": "ML Document Classification Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ml-document-classification.git"}, "bugs": {"url": "https://github.com/your-org/ml-document-classification/issues"}, "homepage": "https://github.com/your-org/ml-document-classification#readme"}