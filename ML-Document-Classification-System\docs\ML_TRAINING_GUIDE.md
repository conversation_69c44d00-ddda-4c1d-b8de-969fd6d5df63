# ML Document Classification Training Guide

This guide provides comprehensive instructions for training the machine learning model to identify various document types including Aadhar cards, Voter IDs, Driving Licenses, and other government documents.

## 🎯 Overview

The ML Document Classification system uses deep learning (CNN) models to:
- **Classify** document types with high accuracy
- **Extract** text and visual features from documents
- **Validate** document authenticity
- **Store** results in database for analysis

### Supported Document Types
- **Aadhar Card** - Indian national ID
- **Voter ID** - Election commission ID
- **Driving License** - Transport department license
- **Passport** - International travel document
- **PAN Card** - Tax identification card
- **Bank Statement** - Financial document
- **Utility Bill** - Service provider bill

## 🚀 Quick Start

### 1. System Requirements
```bash
# Node.js version 14 or higher
node --version

# Required dependencies (already in package.json)
- @tensorflow/tfjs-node
- canvas
- node-tesseract-ocr
- mongoose
```

### 2. One-Command Setup
```bash
# Navigate to server directory
cd server

# Install dependencies
npm install

# Complete ML system setup (recommended)
npm run setup-ml

# OR manual setup
node scripts/setupMLSystem.js
```

### 3. Alternative: Step-by-Step Setup
```bash
# Initialize ML system manually
node scripts/trainModel.js
# Select option 1: Initialize system
```

### 4. Prepare Training Data

The setup script automatically creates the directory structure:
```
server/sample_data/
├── aadhar/          # Place Aadhar card images here
├── voter_id/        # Place Voter ID images here
├── driving_license/ # Place Driving License images here
├── passport/        # Place Passport images here
├── pan_card/        # Place PAN card images here
├── bank_statement/  # Place Bank Statement images here
├── utility_bill/    # Place Utility Bill images here
└── unknown/         # Place other document images here
```

Each folder contains a README.md with specific guidelines.

### 5. Upload and Train

#### Option A: Web Interface (Recommended)
```bash
# Start the server
npm run dev

# Open browser and go to:
# http://localhost:5000/ml-training
```

#### Option B: Command Line
```bash
# Interactive training script
npm run train-ml

# OR
node scripts/trainModel.js
# Select option 3: Upload training data
# Select option 6: Start training
```

### 6. Test the Model
```bash
# Web interface: http://localhost:5000/document-tester
# OR command line: node scripts/trainModel.js (option 7)
```

## 📊 Training Data Requirements

### Minimum Requirements
- **At least 10 samples** per document type
- **Image formats**: JPG, PNG, GIF, BMP
- **File size**: Maximum 10MB per image
- **Resolution**: Minimum 224x224 pixels (will be resized)

### Recommended Dataset
- **Training**: 100-500 samples per document type
- **Validation**: 20-100 samples per document type
- **Test**: 20-100 samples per document type

### Data Quality Guidelines
1. **Clear images** with good lighting
2. **Complete documents** (not cropped)
3. **Various orientations** and conditions
4. **Different document versions** (old/new formats)
5. **Multiple languages** if applicable

## 🔧 Training Configuration

### Default Parameters
```javascript
{
  epochs: 50,           // Number of training iterations
  batchSize: 32,        // Samples processed together
  learningRate: 0.001,  // Model learning speed
  validationSplit: 0.2  // Portion for validation
}
```

### Adjusting Parameters
```bash
node scripts/trainModel.js
# Select option 5: Configure training parameters
```

#### Parameter Guidelines
- **Epochs**: 
  - Small dataset: 30-50 epochs
  - Large dataset: 50-100 epochs
- **Batch Size**: 
  - Limited memory: 16-32
  - Good hardware: 32-64
- **Learning Rate**: 
  - Start with 0.001
  - Reduce if loss oscillates
  - Increase if training is slow

## 🎮 Web Interface Training

### Access the Training Interface
1. Start the server:
```bash
npm run dev
```

2. Navigate to: `http://localhost:5000/ml-training`

### Using the Web Interface

#### Upload Tab
1. Select dataset type (Training/Validation/Test)
2. Choose multiple image files
3. Assign document type to each file
4. Click "Upload Training Data"

#### Training Tab
1. Configure training parameters
2. Initialize training session
3. Start training
4. Monitor progress in real-time

#### Monitoring Tab
1. View training progress charts
2. Check dataset distribution
3. Monitor loss and accuracy

## 🧪 Testing the Model

### Command Line Testing
```bash
node scripts/trainModel.js
# Select option 7: Test trained model
# Enter path to test image
```

### Web Interface Testing
1. Navigate to: `http://localhost:5000/document-tester`
2. Upload a document image
3. View classification results
4. Check authenticity validation

### API Testing
```bash
curl -X POST \
  http://localhost:5000/api/ml-documents/classify \
  -F "document=@/path/to/test/image.jpg"
```

## 📈 Model Performance

### Evaluation Metrics
- **Accuracy**: Overall correct predictions
- **Precision**: Correct positive predictions
- **Recall**: Found positive cases
- **F1-Score**: Harmonic mean of precision/recall

### Expected Performance
- **Good model**: >85% accuracy
- **Excellent model**: >95% accuracy
- **Production ready**: >98% accuracy

### Improving Performance
1. **Add more training data**
2. **Balance dataset** (equal samples per class)
3. **Improve data quality**
4. **Adjust hyperparameters**
5. **Use data augmentation**

## 🗄️ Database Integration

### Document Classification Results
```javascript
// Stored in DocumentClassification collection
{
  documentId: "unique_id",
  classificationResult: {
    documentType: "aadhar",
    confidence: 0.95,
    probabilities: { ... }
  },
  extractedFeatures: { ... },
  validationResult: { ... },
  isTrainingData: false
}
```

### Training Sessions
```javascript
// Stored in TrainingSession collection
{
  sessionId: "training_session_id",
  config: { epochs: 50, ... },
  progress: { currentEpoch: 25, ... },
  metrics: { loss: [...], accuracy: [...] }
}
```

## 🔍 Feature Extraction

### Visual Features
- **CNN features**: Deep learning extracted features
- **Layout analysis**: Brightness, contrast, dimensions
- **Edge detection**: Document structure analysis

### Text Features
- **OCR extraction**: Text content using Tesseract
- **Pattern matching**: ID numbers, dates, addresses
- **Keyword detection**: Government terms, document-specific words

### Authenticity Validation
- **Fraud detection**: Suspicious patterns
- **Quality assessment**: Image quality issues
- **Risk factors**: Potential authenticity problems

## 🚨 Troubleshooting

### Common Issues

#### 1. TensorFlow Installation
```bash
# If TensorFlow fails to install
npm install @tensorflow/tfjs-node --build-from-source
```

#### 2. Canvas Dependencies
```bash
# Ubuntu/Debian
sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev

# macOS
brew install pkg-config cairo pango libpng jpeg giflib librsvg
```

#### 3. Tesseract OCR
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr

# macOS
brew install tesseract

# Windows
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
```

#### 4. Memory Issues
- Reduce batch size
- Use smaller images
- Close other applications
- Increase system RAM

#### 5. Training Fails
- Check data quality
- Verify file paths
- Ensure sufficient disk space
- Monitor system resources

### Performance Optimization

#### 1. Hardware Acceleration
```bash
# For GPU support (NVIDIA CUDA required)
npm install @tensorflow/tfjs-node-gpu

# Note: GPU support requires:
# - NVIDIA GPU with CUDA Compute Capability 3.5+
# - CUDA 11.2 and cuDNN 8.1
# - Windows: Visual Studio 2019 Build Tools
```

#### 2. Memory Management
```javascript
// In training configuration
{
  batchSize: 16,        // Reduce if memory issues
  imageSize: [224, 224] // Standard size
}
```

#### 3. Data Pipeline
- Use SSD storage for training data
- Preprocess images in batches
- Cache preprocessed data

## 📚 API Reference

### Training Endpoints
```javascript
// Initialize training session
POST /api/ml-documents/training/session/init
Body: { config: { epochs: 50, ... } }

// Upload training samples
POST /api/ml-documents/training/samples/upload
FormData: { samples: [files], documentTypes: [...] }

// Start training
POST /api/ml-documents/training/start

// Get training stats
GET /api/ml-documents/training/session/stats

// Get training history
GET /api/ml-documents/training/history
```

### Classification Endpoints
```javascript
// Classify document
POST /api/ml-documents/classify
FormData: { document: file }

// Get supported document types
GET /api/ml-documents/document-types
```

## 🔐 Security Considerations

### Data Privacy
- Training data is stored locally
- No data sent to external services
- Secure file handling and cleanup

### Model Security
- Models stored in secure directories
- Access control for training endpoints
- Audit logging for training sessions

### Production Deployment
- Use HTTPS for API endpoints
- Implement rate limiting
- Add authentication for training endpoints
- Regular security updates

## 📞 Support

### Getting Help
1. Check this guide first
2. Review error logs in `server/logs/`
3. Test with sample data
4. Check system requirements

### Contributing
1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

---

## 🎉 Success Checklist

- [ ] System initialized successfully
- [ ] Training data uploaded (>10 samples per type)
- [ ] Model training completed (>85% accuracy)
- [ ] Test classification working
- [ ] Web interface accessible
- [ ] API endpoints responding
- [ ] Database storing results
- [ ] Authenticity validation working

**Congratulations! Your ML document classification system is ready for production use.**
