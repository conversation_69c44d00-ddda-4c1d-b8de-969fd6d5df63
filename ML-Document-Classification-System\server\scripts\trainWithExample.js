#!/usr/bin/env node

/**
 * Training Example Script
 * 
 * This script demonstrates how to train the ML model with example data.
 * It creates synthetic training data and walks through the complete training process.
 */

const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const documentClassification = require('../ai/documentClassification');
const trainingManager = require('../ai/trainingManager');
const trainingDataManager = require('../ai/trainingDataManager');

// Example training configuration
const EXAMPLE_CONFIG = {
  epochs: 10,           // Reduced for demo
  batchSize: 8,         // Smaller batch for demo
  learningRate: 0.001,
  validationSplit: 0.2,
  samplesPerClass: 20   // 20 samples per document type
};

/**
 * Create synthetic document image
 * @param {string} documentType - Type of document to create
 * @param {number} index - Sample index
 * @returns {Buffer} - Image buffer
 */
function createSyntheticDocument(documentType, index) {
  const canvas = createCanvas(400, 300);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, 400, 300);
  
  // Border
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = 2;
  ctx.strokeRect(10, 10, 380, 280);
  
  // Document type specific content
  ctx.fillStyle = '#000000';
  ctx.font = 'bold 24px Arial';
  
  switch (documentType) {
    case 'aadhar':
      // Aadhar card layout
      ctx.fillText('AADHAAR', 150, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Government of India', 130, 80);
      ctx.fillText(`Name: Sample Person ${index}`, 30, 120);
      ctx.fillText(`DOB: 01/01/199${index % 10}`, 30, 150);
      ctx.fillText(`Aadhaar: 1234 5678 ${String(index).padStart(4, '0')}`, 30, 180);
      ctx.fillText('Address: Sample Address', 30, 210);
      
      // Add some visual elements
      ctx.fillStyle = '#ff6b6b';
      ctx.fillRect(300, 120, 60, 80);
      ctx.fillStyle = '#000000';
      ctx.fillText('PHOTO', 310, 165);
      break;
      
    case 'voter_id':
      // Voter ID layout
      ctx.fillText('VOTER ID CARD', 120, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Election Commission of India', 100, 80);
      ctx.fillText(`Name: Voter ${index}`, 30, 120);
      ctx.fillText(`Father: Father Name`, 30, 150);
      ctx.fillText(`ID: ABC123${String(index).padStart(4, '0')}`, 30, 180);
      ctx.fillText('Constituency: Sample Area', 30, 210);
      
      // Add photo placeholder
      ctx.fillStyle = '#4ecdc4';
      ctx.fillRect(300, 120, 60, 80);
      ctx.fillStyle = '#000000';
      ctx.fillText('PHOTO', 310, 165);
      break;
      
    case 'driving_license':
      // Driving License layout
      ctx.fillText('DRIVING LICENCE', 110, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Transport Department', 120, 80);
      ctx.fillText(`Name: Driver ${index}`, 30, 120);
      ctx.fillText(`DOB: 15/06/198${index % 10}`, 30, 150);
      ctx.fillText(`DL No: DL${String(index).padStart(6, '0')}`, 30, 180);
      ctx.fillText('Valid Till: 15/06/2030', 30, 210);
      
      // Add photo placeholder
      ctx.fillStyle = '#45b7d1';
      ctx.fillRect(300, 120, 60, 80);
      ctx.fillStyle = '#000000';
      ctx.fillText('PHOTO', 310, 165);
      break;
      
    case 'passport':
      // Passport layout
      ctx.fillText('PASSPORT', 150, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Republic of India', 130, 80);
      ctx.fillText(`Name: Passport Holder ${index}`, 30, 120);
      ctx.fillText(`DOB: 20/03/199${index % 10}`, 30, 150);
      ctx.fillText(`Passport No: P${String(index).padStart(7, '0')}`, 30, 180);
      ctx.fillText('Place of Birth: India', 30, 210);
      
      // Add photo placeholder
      ctx.fillStyle = '#96ceb4';
      ctx.fillRect(300, 120, 60, 80);
      ctx.fillStyle = '#000000';
      ctx.fillText('PHOTO', 310, 165);
      break;
      
    case 'pan_card':
      // PAN Card layout
      ctx.fillText('PERMANENT ACCOUNT NUMBER', 80, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Income Tax Department', 110, 80);
      ctx.fillText(`Name: PAN Holder ${index}`, 30, 120);
      ctx.fillText(`Father: Father Name`, 30, 150);
      ctx.fillText(`PAN: ABCDE${String(index).padStart(4, '0')}F`, 30, 180);
      ctx.fillText(`DOB: 10/12/198${index % 10}`, 30, 210);
      
      // Add signature area
      ctx.strokeRect(250, 200, 120, 30);
      ctx.fillText('Signature', 270, 220);
      break;
      
    default:
      // Generic document
      ctx.fillText('SAMPLE DOCUMENT', 110, 50);
      ctx.font = '16px Arial';
      ctx.fillText('Official Document', 130, 80);
      ctx.fillText(`Document ID: ${index}`, 30, 120);
      ctx.fillText('Sample Content', 30, 150);
      break;
  }
  
  // Add some noise for realism
  for (let i = 0; i < 50; i++) {
    ctx.fillStyle = `rgba(0,0,0,${Math.random() * 0.1})`;
    ctx.fillRect(Math.random() * 400, Math.random() * 300, 1, 1);
  }
  
  return canvas.toBuffer('image/jpeg', { quality: 0.8 });
}

/**
 * Generate example training data
 * @returns {Promise<Object>} - Generation result
 */
async function generateExampleData() {
  console.log('📝 Generating example training data...');
  
  const sampleDataPath = path.join(__dirname, '../sample_data');
  const documentTypes = ['aadhar', 'voter_id', 'driving_license', 'passport', 'pan_card'];
  
  // Create directories
  if (!fs.existsSync(sampleDataPath)) {
    fs.mkdirSync(sampleDataPath, { recursive: true });
  }
  
  let totalGenerated = 0;
  
  for (const docType of documentTypes) {
    const docTypePath = path.join(sampleDataPath, docType);
    
    if (!fs.existsSync(docTypePath)) {
      fs.mkdirSync(docTypePath, { recursive: true });
    }
    
    console.log(`  Generating ${EXAMPLE_CONFIG.samplesPerClass} samples for ${docType}...`);
    
    for (let i = 1; i <= EXAMPLE_CONFIG.samplesPerClass; i++) {
      const imageBuffer = createSyntheticDocument(docType, i);
      const fileName = `${docType}_sample_${String(i).padStart(3, '0')}.jpg`;
      const filePath = path.join(docTypePath, fileName);
      
      fs.writeFileSync(filePath, imageBuffer);
      totalGenerated++;
    }
  }
  
  console.log(`✅ Generated ${totalGenerated} training samples`);
  console.log(`📁 Location: ${sampleDataPath}`);
  
  return {
    success: true,
    totalSamples: totalGenerated,
    sampleDataPath: sampleDataPath
  };
}

/**
 * Run complete training example
 */
async function runTrainingExample() {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 ML DOCUMENT CLASSIFICATION TRAINING EXAMPLE');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Initialize system
    console.log('\n📋 Step 1: Initializing ML system...');
    const initSuccess = await documentClassification.initializeDocumentClassification();
    
    if (!initSuccess) {
      throw new Error('Failed to initialize ML system');
    }
    console.log('✅ ML system initialized successfully');
    
    // Step 2: Generate example data
    console.log('\n📋 Step 2: Generating example training data...');
    const dataGenResult = await generateExampleData();
    
    if (!dataGenResult.success) {
      throw new Error('Failed to generate example data');
    }
    
    // Step 3: Upload training data
    console.log('\n📋 Step 3: Organizing training data...');
    
    const trainingFiles = [];
    const documentTypes = ['aadhar', 'voter_id', 'driving_license', 'passport', 'pan_card'];
    
    documentTypes.forEach(docType => {
      const docTypePath = path.join(dataGenResult.sampleDataPath, docType);
      const files = fs.readdirSync(docTypePath);
      
      files.forEach((file, index) => {
        if (file.endsWith('.jpg')) {
          const filePath = path.join(docTypePath, file);
          
          // Split data: 60% training, 20% validation, 20% test
          let split = 'train';
          if (index >= EXAMPLE_CONFIG.samplesPerClass * 0.6) {
            split = index >= EXAMPLE_CONFIG.samplesPerClass * 0.8 ? 'test' : 'validation';
          }
          
          trainingFiles.push({
            filePath: filePath,
            documentType: docType,
            split: split
          });
        }
      });
    });
    
    console.log(`📊 Organizing ${trainingFiles.length} files...`);
    
    const organizeResult = await trainingDataManager.organizeTrainingData(trainingFiles);
    
    if (!organizeResult.success) {
      throw new Error('Failed to organize training data');
    }
    
    console.log(`✅ Organized ${organizeResult.results.organized} files`);
    if (organizeResult.results.failed > 0) {
      console.log(`⚠️  Failed to organize ${organizeResult.results.failed} files`);
    }
    
    // Step 4: View training statistics
    console.log('\n📋 Step 4: Training data statistics...');
    const stats = await trainingDataManager.getTrainingDataStats();
    
    if (stats.success) {
      console.log(`📈 Total samples: ${stats.databaseStats.totalSamples}`);
      console.log('📊 Distribution by document type:');
      Object.entries(stats.databaseStats.byDocumentType).forEach(([type, count]) => {
        console.log(`   ${type}: ${count} samples`);
      });
      console.log('🗂️  Distribution by dataset:');
      Object.entries(stats.databaseStats.byDataset).forEach(([dataset, count]) => {
        console.log(`   ${dataset}: ${count} samples`);
      });
    }
    
    // Step 5: Initialize training session
    console.log('\n📋 Step 5: Initializing training session...');
    const session = trainingManager.initializeTrainingSession(EXAMPLE_CONFIG);
    console.log(`✅ Training session initialized: ${session.id}`);
    
    // Step 6: Prepare training dataset
    console.log('\n📋 Step 6: Preparing training dataset...');
    const dataset = await trainingDataManager.prepareTrainingDataset();
    
    if (!dataset.success) {
      throw new Error(`Failed to prepare dataset: ${dataset.error}`);
    }
    
    console.log('📊 Dataset prepared successfully');
    console.log('📈 Class distribution:');
    Object.entries(dataset.classStats).forEach(([docType, stats]) => {
      console.log(`   ${docType}: ${stats.training} train, ${stats.validation} val, ${stats.test} test`);
    });
    
    // Step 7: Add training samples
    console.log('\n📋 Step 7: Adding training samples to session...');
    const trainingFilesForSession = [];
    
    Object.entries(dataset.dataset.training).forEach(([docType, docs]) => {
      docs.forEach(doc => {
        trainingFilesForSession.push({
          filePath: doc.filePath,
          documentType: docType
        });
      });
    });
    
    const addResult = trainingManager.addTrainingSamples(
      trainingFilesForSession,
      'training'
    );
    
    if (!addResult.success) {
      throw new Error(`Failed to add training samples: ${addResult.error}`);
    }
    
    console.log(`✅ Added ${addResult.samplesAdded} training samples`);
    
    // Step 8: Start training
    console.log('\n📋 Step 8: Starting model training...');
    console.log('⏳ This may take several minutes...');
    console.log(`🎯 Training with ${EXAMPLE_CONFIG.epochs} epochs, batch size ${EXAMPLE_CONFIG.batchSize}`);
    
    const trainingResult = await trainingManager.startTraining();
    
    if (trainingResult.success) {
      console.log('\n🎉 Training completed successfully!');
      console.log(`📊 Final accuracy: ${(trainingResult.trainingStats.bestAccuracy * 100).toFixed(2)}%`);
      console.log(`⏱️  Training time: ${Math.round(trainingResult.trainingStats.trainingTime / 1000)}s`);
    } else {
      throw new Error(`Training failed: ${trainingResult.error}`);
    }
    
    // Step 9: Test the model
    console.log('\n📋 Step 9: Testing the trained model...');
    
    // Get a test sample
    const testSample = trainingFiles.find(f => f.split === 'test');
    if (testSample) {
      console.log(`🧪 Testing with: ${path.basename(testSample.filePath)}`);
      console.log(`📋 Expected type: ${testSample.documentType}`);
      
      const imageBuffer = fs.readFileSync(testSample.filePath);
      const classificationResult = await documentClassification.classifyDocument(imageBuffer);
      
      if (classificationResult.success) {
        console.log(`🎯 Predicted type: ${classificationResult.documentType}`);
        console.log(`📊 Confidence: ${(classificationResult.confidence * 100).toFixed(2)}%`);
        console.log(`✅ Correct: ${classificationResult.documentType === testSample.documentType ? 'YES' : 'NO'}`);
        
        // Show top 3 predictions
        console.log('\n📊 Top predictions:');
        const sortedProbs = Object.entries(classificationResult.probabilities)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3);
        
        sortedProbs.forEach(([type, prob], index) => {
          console.log(`   ${index + 1}. ${type}: ${(prob * 100).toFixed(2)}%`);
        });
      } else {
        console.log(`❌ Classification failed: ${classificationResult.error}`);
      }
    }
    
    // Step 10: Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎉 TRAINING EXAMPLE COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(60));
    console.log('\n📋 Summary:');
    console.log(`✅ Generated ${dataGenResult.totalSamples} synthetic training samples`);
    console.log(`✅ Organized data into training/validation/test sets`);
    console.log(`✅ Trained model for ${EXAMPLE_CONFIG.epochs} epochs`);
    console.log(`✅ Achieved ${(trainingResult.trainingStats.bestAccuracy * 100).toFixed(2)}% accuracy`);
    console.log(`✅ Successfully tested model on sample data`);
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Add real document images to server/sample_data/');
    console.log('2. Retrain with more data for better accuracy');
    console.log('3. Use the web interface: http://localhost:5000/ml-training');
    console.log('4. Test documents: http://localhost:5000/document-tester');
    
    console.log('\n📚 Available Commands:');
    console.log('- node scripts/trainModel.js     # Interactive training');
    console.log('- npm run train-ml               # Interactive training');
    console.log('- npm run dev                    # Start web server');
    
    console.log('\n' + '='.repeat(60));
    
  } catch (error) {
    console.error('\n❌ Training example failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the example if this script is executed directly
if (require.main === module) {
  runTrainingExample().then(() => {
    console.log('\n👋 Training example completed. Exiting...');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  runTrainingExample,
  generateExampleData,
  createSyntheticDocument,
  EXAMPLE_CONFIG
};
