/**
 * Model Evaluator Module
 * 
 * This module provides comprehensive model evaluation capabilities including
 * accuracy testing, confusion matrix generation, and performance metrics.
 */

const fs = require('fs');
const path = require('path');
const documentClassification = require('./documentClassification');
const { ModelPerformance, DocumentClassification: DocClassModel } = require('../models/DocumentClassification');

/**
 * Evaluate model performance on test dataset
 * @param {Object} options - Evaluation options
 * @returns {Promise<Object>} - Evaluation results
 */
async function evaluateModel(options = {}) {
  try {
    const {
      testDataPath = null,
      useStoredTestData = true,
      saveResults = true,
      modelVersion = '1.0.0'
    } = options;

    console.log('Starting model evaluation...');

    let testData = [];

    if (useStoredTestData) {
      // Get test data from database
      const storedTestData = await DocClassModel.find({
        isTrainingData: true,
        trainingDataset: 'test'
      });

      testData = storedTestData.map(doc => ({
        filePath: doc.filePath,
        actualType: doc.classificationResult.documentType,
        documentId: doc.documentId
      }));
    } else if (testDataPath) {
      // Load test data from directory
      testData = await loadTestDataFromDirectory(testDataPath);
    } else {
      throw new Error('No test data source specified');
    }

    if (testData.length === 0) {
      throw new Error('No test data available');
    }

    console.log(`Evaluating on ${testData.length} test samples...`);

    // Initialize evaluation metrics
    const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);
    const confusionMatrix = {};
    const classMetrics = {};
    const predictions = [];

    // Initialize confusion matrix
    documentTypes.forEach(actualType => {
      confusionMatrix[actualType] = {};
      documentTypes.forEach(predictedType => {
        confusionMatrix[actualType][predictedType] = 0;
      });
    });

    // Initialize class metrics
    documentTypes.forEach(type => {
      classMetrics[type] = {
        truePositives: 0,
        falsePositives: 0,
        falseNegatives: 0,
        trueNegatives: 0
      };
    });

    // Evaluate each test sample
    let correctPredictions = 0;
    const startTime = Date.now();

    for (let i = 0; i < testData.length; i++) {
      const sample = testData[i];
      
      try {
        // Read and classify the document
        const imageBuffer = fs.readFileSync(sample.filePath);
        const result = await documentClassification.classifyDocument(imageBuffer);

        if (result.success) {
          const predictedType = result.documentType;
          const actualType = sample.actualType;

          // Update confusion matrix
          confusionMatrix[actualType][predictedType]++;

          // Check if prediction is correct
          if (predictedType === actualType) {
            correctPredictions++;
          }

          // Store prediction details
          predictions.push({
            documentId: sample.documentId,
            actualType: actualType,
            predictedType: predictedType,
            confidence: result.confidence,
            probabilities: result.probabilities,
            correct: predictedType === actualType
          });

          // Update class metrics
          documentTypes.forEach(type => {
            if (type === actualType && type === predictedType) {
              classMetrics[type].truePositives++;
            } else if (type === actualType && type !== predictedType) {
              classMetrics[type].falseNegatives++;
            } else if (type !== actualType && type === predictedType) {
              classMetrics[type].falsePositives++;
            } else {
              classMetrics[type].trueNegatives++;
            }
          });

        } else {
          console.error(`Failed to classify ${sample.filePath}: ${result.error}`);
          
          // Count as incorrect prediction
          predictions.push({
            documentId: sample.documentId,
            actualType: sample.actualType,
            predictedType: 'unknown',
            confidence: 0,
            probabilities: {},
            correct: false,
            error: result.error
          });
        }

        // Progress indicator
        if ((i + 1) % 10 === 0) {
          console.log(`Processed ${i + 1}/${testData.length} samples...`);
        }

      } catch (error) {
        console.error(`Error processing ${sample.filePath}:`, error.message);
        
        predictions.push({
          documentId: sample.documentId,
          actualType: sample.actualType,
          predictedType: 'error',
          confidence: 0,
          probabilities: {},
          correct: false,
          error: error.message
        });
      }
    }

    const evaluationTime = Date.now() - startTime;

    // Calculate overall metrics
    const accuracy = correctPredictions / testData.length;

    // Calculate per-class metrics
    const perClassMetrics = {};
    let totalPrecision = 0;
    let totalRecall = 0;
    let totalF1 = 0;
    let validClasses = 0;

    documentTypes.forEach(type => {
      const metrics = classMetrics[type];
      const precision = metrics.truePositives / (metrics.truePositives + metrics.falsePositives) || 0;
      const recall = metrics.truePositives / (metrics.truePositives + metrics.falseNegatives) || 0;
      const f1Score = 2 * (precision * recall) / (precision + recall) || 0;
      const support = metrics.truePositives + metrics.falseNegatives;

      perClassMetrics[type] = {
        precision: precision,
        recall: recall,
        f1Score: f1Score,
        support: support
      };

      if (support > 0) {
        totalPrecision += precision;
        totalRecall += recall;
        totalF1 += f1Score;
        validClasses++;
      }
    });

    // Calculate macro averages
    const macroPrecision = validClasses > 0 ? totalPrecision / validClasses : 0;
    const macroRecall = validClasses > 0 ? totalRecall / validClasses : 0;
    const macroF1 = validClasses > 0 ? totalF1 / validClasses : 0;

    // Prepare evaluation results
    const evaluationResults = {
      modelVersion: modelVersion,
      evaluationDate: new Date(),
      testDataset: {
        totalSamples: testData.length,
        samplesPerClass: getSamplesPerClass(testData),
        evaluationTime: evaluationTime
      },
      overallMetrics: {
        accuracy: accuracy,
        precision: macroPrecision,
        recall: macroRecall,
        f1Score: macroF1
      },
      perClassMetrics: perClassMetrics,
      confusionMatrix: confusionMatrix,
      predictions: predictions,
      summary: {
        correctPredictions: correctPredictions,
        incorrectPredictions: testData.length - correctPredictions,
        averageConfidence: predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length,
        processingTimePerSample: evaluationTime / testData.length
      }
    };

    // Save results to database if requested
    if (saveResults) {
      await saveEvaluationResults(evaluationResults);
    }

    console.log('Model evaluation completed successfully');
    console.log(`Overall Accuracy: ${(accuracy * 100).toFixed(2)}%`);
    console.log(`Macro F1-Score: ${(macroF1 * 100).toFixed(2)}%`);

    return {
      success: true,
      results: evaluationResults
    };

  } catch (error) {
    console.error('Error during model evaluation:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Load test data from directory structure
 * @param {string} testDataPath - Path to test data directory
 * @returns {Promise<Array>} - Array of test samples
 */
async function loadTestDataFromDirectory(testDataPath) {
  const testData = [];
  const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);

  documentTypes.forEach(docType => {
    const docTypePath = path.join(testDataPath, docType);
    
    if (fs.existsSync(docTypePath)) {
      const files = fs.readdirSync(docTypePath);
      
      files.forEach(file => {
        const filePath = path.join(docTypePath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile() && file.match(/\.(jpg|jpeg|png|gif|bmp)$/i)) {
          testData.push({
            filePath: filePath,
            actualType: docType,
            documentId: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          });
        }
      });
    }
  });

  return testData;
}

/**
 * Get samples per class distribution
 * @param {Array} testData - Test data array
 * @returns {Object} - Samples per class
 */
function getSamplesPerClass(testData) {
  const distribution = {};
  
  testData.forEach(sample => {
    distribution[sample.actualType] = (distribution[sample.actualType] || 0) + 1;
  });

  return distribution;
}

/**
 * Save evaluation results to database
 * @param {Object} results - Evaluation results
 * @returns {Promise<void>}
 */
async function saveEvaluationResults(results) {
  try {
    const modelPerformance = new ModelPerformance({
      modelVersion: results.modelVersion,
      metrics: {
        accuracy: results.overallMetrics.accuracy,
        precision: results.overallMetrics.precision,
        recall: results.overallMetrics.recall,
        f1Score: results.overallMetrics.f1Score,
        confusionMatrix: convertConfusionMatrixToArray(results.confusionMatrix),
        classificationReport: results.perClassMetrics
      },
      classMetrics: results.perClassMetrics,
      testDataset: {
        totalSamples: results.testDataset.totalSamples,
        samplesPerClass: results.testDataset.samplesPerClass,
        testDate: results.evaluationDate
      },
      modelInfo: {
        modelVersion: results.modelVersion,
        parameters: 0 // TODO: Calculate actual parameter count
      },
      evaluatedAt: results.evaluationDate
    });

    await modelPerformance.save();
    console.log('Evaluation results saved to database');
  } catch (error) {
    console.error('Error saving evaluation results:', error);
  }
}

/**
 * Convert confusion matrix object to 2D array
 * @param {Object} confusionMatrix - Confusion matrix object
 * @returns {Array} - 2D array representation
 */
function convertConfusionMatrixToArray(confusionMatrix) {
  const documentTypes = Object.values(documentClassification.DOCUMENT_TYPES);
  const matrix = [];

  documentTypes.forEach(actualType => {
    const row = [];
    documentTypes.forEach(predictedType => {
      row.push(confusionMatrix[actualType][predictedType] || 0);
    });
    matrix.push(row);
  });

  return matrix;
}

/**
 * Generate detailed evaluation report
 * @param {Object} results - Evaluation results
 * @returns {string} - Formatted report
 */
function generateEvaluationReport(results) {
  let report = '\n' + '='.repeat(60) + '\n';
  report += '📊 MODEL EVALUATION REPORT\n';
  report += '='.repeat(60) + '\n';
  
  report += `Model Version: ${results.modelVersion}\n`;
  report += `Evaluation Date: ${results.evaluationDate.toLocaleString()}\n`;
  report += `Test Samples: ${results.testDataset.totalSamples}\n`;
  report += `Evaluation Time: ${Math.round(results.testDataset.evaluationTime / 1000)}s\n\n`;

  // Overall metrics
  report += '📈 OVERALL METRICS\n';
  report += '-'.repeat(30) + '\n';
  report += `Accuracy: ${(results.overallMetrics.accuracy * 100).toFixed(2)}%\n`;
  report += `Precision: ${(results.overallMetrics.precision * 100).toFixed(2)}%\n`;
  report += `Recall: ${(results.overallMetrics.recall * 100).toFixed(2)}%\n`;
  report += `F1-Score: ${(results.overallMetrics.f1Score * 100).toFixed(2)}%\n\n`;

  // Per-class metrics
  report += '📋 PER-CLASS METRICS\n';
  report += '-'.repeat(30) + '\n';
  Object.entries(results.perClassMetrics).forEach(([className, metrics]) => {
    if (metrics.support > 0) {
      report += `${className.toUpperCase()}:\n`;
      report += `  Precision: ${(metrics.precision * 100).toFixed(2)}%\n`;
      report += `  Recall: ${(metrics.recall * 100).toFixed(2)}%\n`;
      report += `  F1-Score: ${(metrics.f1Score * 100).toFixed(2)}%\n`;
      report += `  Support: ${metrics.support}\n\n`;
    }
  });

  // Confusion matrix
  report += '🔢 CONFUSION MATRIX\n';
  report += '-'.repeat(30) + '\n';
  const documentTypes = Object.keys(results.confusionMatrix);
  
  // Header
  report += '        ';
  documentTypes.forEach(type => {
    report += `${type.substr(0, 8).padEnd(8)} `;
  });
  report += '\n';

  // Matrix rows
  documentTypes.forEach(actualType => {
    report += `${actualType.substr(0, 8).padEnd(8)} `;
    documentTypes.forEach(predictedType => {
      const count = results.confusionMatrix[actualType][predictedType];
      report += `${count.toString().padEnd(8)} `;
    });
    report += '\n';
  });

  report += '\n' + '='.repeat(60) + '\n';

  return report;
}

/**
 * Get model performance history
 * @returns {Promise<Array>} - Performance history
 */
async function getModelPerformanceHistory() {
  try {
    const history = await ModelPerformance.find()
      .sort({ evaluatedAt: -1 })
      .limit(10);

    return {
      success: true,
      history: history
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Compare model versions
 * @param {string} version1 - First model version
 * @param {string} version2 - Second model version
 * @returns {Promise<Object>} - Comparison results
 */
async function compareModelVersions(version1, version2) {
  try {
    const model1 = await ModelPerformance.findOne({ modelVersion: version1 })
      .sort({ evaluatedAt: -1 });
    const model2 = await ModelPerformance.findOne({ modelVersion: version2 })
      .sort({ evaluatedAt: -1 });

    if (!model1 || !model2) {
      throw new Error('One or both model versions not found');
    }

    const comparison = {
      version1: {
        version: version1,
        accuracy: model1.metrics.accuracy,
        f1Score: model1.metrics.f1Score,
        evaluatedAt: model1.evaluatedAt
      },
      version2: {
        version: version2,
        accuracy: model2.metrics.accuracy,
        f1Score: model2.metrics.f1Score,
        evaluatedAt: model2.evaluatedAt
      },
      improvements: {
        accuracy: model2.metrics.accuracy - model1.metrics.accuracy,
        f1Score: model2.metrics.f1Score - model1.metrics.f1Score
      }
    };

    return {
      success: true,
      comparison: comparison
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  evaluateModel,
  loadTestDataFromDirectory,
  getSamplesPerClass,
  saveEvaluationResults,
  generateEvaluationReport,
  getModelPerformanceHistory,
  compareModelVersions
};
