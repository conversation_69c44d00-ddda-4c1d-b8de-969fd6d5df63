/**
 * ML Document Routes Module
 * 
 * This module defines the API routes for machine learning document identification,
 * training, and management features.
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Import ML modules
const documentClassification = require('../ai/documentClassification');
const trainingManager = require('../ai/trainingManager');
const modelEvaluator = require('../ai/modelEvaluator');

// Initialize ML systems on startup
(async () => {
  try {
    await documentClassification.initializeDocumentClassification();
    console.log('Document classification system initialized');
  } catch (error) {
    console.error('Failed to initialize document classification:', error);
  }
})();

// Import middleware
const errorHandler = require('../utils/errorHandler');
const logger = require('../utils/logger');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/ml_training');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 50 // Maximum 50 files per request
  },
  fileFilter: (req, file, cb) => {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

/**
 * @route POST /api/ml-documents/classify
 * @desc Classify a document image using ML
 * @access Private
 */
router.post('/classify', upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No document image provided'
      });
    }

    logger.info(`Document classification request for file: ${req.file.filename}`);

    // Read the uploaded file
    const imageBuffer = fs.readFileSync(req.file.path);

    // Classify the document
    const classificationResult = await documentClassification.classifyDocument(imageBuffer);

    // Extract additional features
    const featuresResult = await documentClassification.extractDocumentFeatures(imageBuffer);

    // Validate authenticity
    const validationResult = await documentClassification.validateDocumentAuthenticity(imageBuffer);

    // Save classification result to database
    try {
      const { DocumentClassification } = require('../models/DocumentClassification');

      const docClassification = new DocumentClassification({
        originalFilename: req.file.originalname,
        filePath: req.file.path,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        classificationResult: {
          documentType: classificationResult.documentType,
          confidence: classificationResult.confidence,
          probabilities: classificationResult.probabilities
        },
        extractedFeatures: featuresResult.success ? featuresResult : null,
        validationResult: {
          isAuthentic: validationResult.success ? validationResult.isAuthentic : false,
          confidence: validationResult.success ? validationResult.confidence : 0,
          authenticityScore: validationResult.success ? validationResult.authenticity_score : 0,
          riskFactors: validationResult.success ? validationResult.risk_factors : []
        },
        processingTime: Date.now() - Date.now(), // Will be calculated properly in production
        userId: req.user?.id || null,
        sessionId: req.sessionID || null,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });

      await docClassification.save();
      logger.info(`Classification result saved to database: ${docClassification.documentId}`);
    } catch (dbError) {
      logger.error('Error saving classification to database:', dbError);
      // Continue without failing the request
    }

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    const response = {
      success: true,
      classification: classificationResult,
      features: featuresResult.success ? featuresResult : null,
      validation: validationResult.success ? validationResult : null,
      timestamp: new Date().toISOString()
    };

    logger.info(`Document classification completed: ${classificationResult.documentType} (confidence: ${classificationResult.confidence})`);

    res.json(response);
  } catch (error) {
    logger.error('Error in document classification:', error);
    
    // Clean up file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/ml-documents/training/session/init
 * @desc Initialize a new training session
 * @access Private
 */
router.post('/training/session/init', async (req, res) => {
  try {
    const config = req.body.config || {};
    
    logger.info('Initializing new training session with config:', config);
    
    const session = trainingManager.initializeTrainingSession(config);
    
    res.json({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        config: session.config,
        startTime: session.startTime
      }
    });
  } catch (error) {
    logger.error('Error initializing training session:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/ml-documents/training/samples/upload
 * @desc Upload training samples
 * @access Private
 */
router.post('/training/samples/upload', upload.array('samples', 50), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No training samples provided'
      });
    }

    const { documentTypes, datasetType = 'training' } = req.body;
    
    if (!documentTypes) {
      return res.status(400).json({
        success: false,
        error: 'Document types not specified'
      });
    }

    // Parse document types (should be JSON string)
    let parsedDocumentTypes;
    try {
      parsedDocumentTypes = JSON.parse(documentTypes);
    } catch (e) {
      return res.status(400).json({
        success: false,
        error: 'Invalid document types format'
      });
    }

    if (parsedDocumentTypes.length !== req.files.length) {
      return res.status(400).json({
        success: false,
        error: 'Number of document types must match number of files'
      });
    }

    logger.info(`Uploading ${req.files.length} training samples to ${datasetType} dataset`);

    // Prepare samples
    const samples = req.files.map((file, index) => ({
      filePath: file.path,
      documentType: parsedDocumentTypes[index],
      metadata: {
        originalName: file.originalname,
        size: file.size,
        uploadedAt: new Date()
      }
    }));

    // Add samples to training session
    const result = trainingManager.addTrainingSamples(samples, datasetType);

    if (result.success) {
      logger.info(`Successfully added ${result.samplesAdded} samples to training session`);
    }

    res.json(result);
  } catch (error) {
    logger.error('Error uploading training samples:', error);
    
    // Clean up uploaded files
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/ml-documents/training/session/stats
 * @desc Get current training session statistics
 * @access Private
 */
router.get('/training/session/stats', async (req, res) => {
  try {
    const stats = trainingManager.getTrainingSessionStats();
    
    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    logger.error('Error getting training session stats:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/ml-documents/training/start
 * @desc Start training with current session data
 * @access Private
 */
router.post('/training/start', async (req, res) => {
  try {
    logger.info('Starting ML model training...');
    
    const trainingResult = await trainingManager.startTraining();
    
    if (trainingResult.success) {
      logger.info(`Training completed successfully for session: ${trainingResult.sessionId}`);
    } else {
      logger.error(`Training failed: ${trainingResult.error}`);
    }
    
    res.json(trainingResult);
  } catch (error) {
    logger.error('Error starting training:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/ml-documents/training/history
 * @desc Get training history
 * @access Private
 */
router.get('/training/history', async (req, res) => {
  try {
    const history = trainingManager.getTrainingHistory();
    
    res.json({
      success: true,
      history: history
    });
  } catch (error) {
    logger.error('Error getting training history:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/ml-documents/training/session/clear
 * @desc Clear current training session
 * @access Private
 */
router.delete('/training/session/clear', async (req, res) => {
  try {
    trainingManager.clearCurrentSession();
    
    logger.info('Training session cleared');
    
    res.json({
      success: true,
      message: 'Training session cleared successfully'
    });
  } catch (error) {
    logger.error('Error clearing training session:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/ml-documents/evaluate
 * @desc Evaluate model performance on test dataset
 * @access Private
 */
router.post('/evaluate', async (req, res) => {
  try {
    const { modelVersion = '1.0.0', useStoredTestData = true } = req.body;

    logger.info('Starting model evaluation...');

    const evaluationResult = await modelEvaluator.evaluateModel({
      modelVersion: modelVersion,
      useStoredTestData: useStoredTestData,
      saveResults: true
    });

    if (evaluationResult.success) {
      logger.info(`Model evaluation completed. Accuracy: ${(evaluationResult.results.overallMetrics.accuracy * 100).toFixed(2)}%`);

      res.json({
        success: true,
        evaluation: evaluationResult.results,
        report: modelEvaluator.generateEvaluationReport(evaluationResult.results)
      });
    } else {
      logger.error(`Model evaluation failed: ${evaluationResult.error}`);
      res.status(500).json({
        success: false,
        error: evaluationResult.error
      });
    }
  } catch (error) {
    logger.error('Error during model evaluation:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/ml-documents/performance/history
 * @desc Get model performance history
 * @access Private
 */
router.get('/performance/history', async (req, res) => {
  try {
    const historyResult = await modelEvaluator.getModelPerformanceHistory();

    if (historyResult.success) {
      res.json({
        success: true,
        history: historyResult.history
      });
    } else {
      res.status(500).json({
        success: false,
        error: historyResult.error
      });
    }
  } catch (error) {
    logger.error('Error getting performance history:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route POST /api/ml-documents/performance/compare
 * @desc Compare two model versions
 * @access Private
 */
router.post('/performance/compare', async (req, res) => {
  try {
    const { version1, version2 } = req.body;

    if (!version1 || !version2) {
      return res.status(400).json({
        success: false,
        error: 'Both version1 and version2 are required'
      });
    }

    const comparisonResult = await modelEvaluator.compareModelVersions(version1, version2);

    if (comparisonResult.success) {
      res.json({
        success: true,
        comparison: comparisonResult.comparison
      });
    } else {
      res.status(500).json({
        success: false,
        error: comparisonResult.error
      });
    }
  } catch (error) {
    logger.error('Error comparing model versions:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route GET /api/ml-documents/document-types
 * @desc Get supported document types
 * @access Public
 */
router.get('/document-types', async (req, res) => {
  try {
    res.json({
      success: true,
      documentTypes: documentClassification.DOCUMENT_TYPES,
      supportedTypes: Object.values(documentClassification.DOCUMENT_TYPES)
    });
  } catch (error) {
    logger.error('Error getting document types:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
